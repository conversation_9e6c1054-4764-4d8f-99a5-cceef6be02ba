<!DOCTYPE html>
<html>
<head>
    <!--
      If you are serving your web app in a path other than the root, change the
      href value below to reflect the base path you are serving from.

      The path provided below has to start and end with a slash "/" in order for
      it to work correctly.

      For more details:
      * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

      This is a placeholder for base href that will be replaced by the value of
      the `--base-href` argument provided to `flutter build`.
    -->
    <base href="$FLUTTER_BASE_HREF">

    <meta charset="UTF-8">
    <meta content="IE=Edge" http-equiv="X-UA-Compatible">
    <meta name="description" content="Appwrite StarterKit in Flutter.">

    <!-- iOS meta tags & icons -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="AppwriteStarterKit">
    <link rel="apple-touch-icon" href="icons/Icon-192.png">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="favicon.svg">

    <title>AppwriteStarterKit</title>
    <link rel="manifest" href="manifest.json">

    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
          name="viewport">

    <style id="splash-screen-style">
        html, body {
          height: 100%;
          margin: 0;
          background-color: #EDEDF0;
        }

        .center {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 64px;
          height: 64px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .bottom {
          position: absolute;
          bottom: 0;
          left: 50%;
          padding-bottom: 24px;
          transform: translateX(-50%);
        }

        @keyframes rotate {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }

        .rotate {
          width: 100%;
          height: 100%;
          transform-origin: center center;
          animation: rotate 1.5s infinite linear;
        }

        @media (prefers-color-scheme: dark) {
          body {
            background-color: #19191D;
          }
        }

        @media (prefers-reduced-motion: reduce) {
          .rotate {
            animation: none;
          }
        }
    </style>

    <script id="splash-screen-script">
        function removeSplashFromWeb() {
          document.getElementById("splash")?.remove();
          document.getElementById("splash-branding")?.remove();
          document.body.style.background = "transparent";
        }
    </script>
</head>
<body>

<picture id="splash-branding">
    <source srcset="splash/img/branding-1x.png 1x, splash/img/branding-2x.png 2x, splash/img/branding-3x.png 3x, splash/img/branding-4x.png 4x"
            media="(prefers-color-scheme: light)">
    <source srcset="splash/img/branding-dark-1x.png 1x, splash/img/branding-dark-2x.png 2x, splash/img/branding-dark-3x.png 3x, splash/img/branding-dark-4x.png 4x"
            media="(prefers-color-scheme: dark)">
    <img class="bottom" aria-hidden="true" src="splash/img/branding-1x.png" alt="">
</picture>

<div id="splash" class="center">
    <svg class="rotate" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none">
        <path opacity="0.2"
              d="M24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0C18.6274 0 24 5.37258 24 12ZM2.4 12C2.4 17.3019 6.69807 21.6 12 21.6C17.3019 21.6 21.6 17.3019 21.6 12C21.6 6.69807 17.3019 2.4 12 2.4C6.69807 2.4 2.4 6.69807 2.4 12Z"
              fill="#56565C"/>
        <path d="M5.11708 2.17017C6.66833 1.08398 8.45463 0.380338 10.3299 0.116783C12.2052 -0.146772 14.1163 0.0372409 15.9068 0.653778C17.6974 1.27031 19.3166 2.30187 20.6321 3.6641C21.9476 5.02633 22.922 6.68056 23.4757 8.49154C24.0293 10.3025 24.1465 12.2188 23.8177 14.0838C23.4888 15.9487 22.7233 17.7094 21.5836 19.2218C20.444 20.7342 18.9625 21.9554 17.2605 22.7855C15.5584 23.6157 13.684 24.0312 11.7906 23.9982L11.8325 21.5985C13.3472 21.625 14.8467 21.2925 16.2084 20.6284C17.57 19.9643 18.7552 18.9873 19.6669 17.7774C20.5786 16.5675 21.1911 15.159 21.4542 13.667C21.7172 12.1751 21.6235 10.642 21.1805 9.19323C20.7376 7.74445 19.9581 6.42107 18.9057 5.33128C17.8533 4.2415 16.5579 3.41625 15.1255 2.92302C13.693 2.42979 12.1642 2.28258 10.6639 2.49343C9.1637 2.70427 7.73467 3.26718 6.49367 4.13614L5.11708 2.17017Z"
              fill="#56565C"/>
    </svg>
</div>

<script src="flutter_bootstrap.js" async=""></script>
</body>
</html>
