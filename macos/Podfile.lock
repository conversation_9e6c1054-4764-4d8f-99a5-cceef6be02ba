PODS:
  - desktop_webview_window (0.0.1):
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - flutter_web_auth_2 (3.0.0):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - screen_retriever_macos (0.0.1):
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - window_manager (0.2.0):
    - FlutterMacOS
  - window_to_front (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - desktop_webview_window (from `Flutter/ephemeral/.symlinks/plugins/desktop_webview_window/macos`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - flutter_web_auth_2 (from `Flutter/ephemeral/.symlinks/plugins/flutter_web_auth_2/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - screen_retriever_macos (from `Flutter/ephemeral/.symlinks/plugins/screen_retriever_macos/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - window_manager (from `Flutter/ephemeral/.symlinks/plugins/window_manager/macos`)
  - window_to_front (from `Flutter/ephemeral/.symlinks/plugins/window_to_front/macos`)

EXTERNAL SOURCES:
  desktop_webview_window:
    :path: Flutter/ephemeral/.symlinks/plugins/desktop_webview_window/macos
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  flutter_web_auth_2:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_web_auth_2/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  screen_retriever_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_retriever_macos/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  window_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/window_manager/macos
  window_to_front:
    :path: Flutter/ephemeral/.symlinks/plugins/window_to_front/macos

SPEC CHECKSUMS:
  desktop_webview_window: 7e37af677d6d19294cb433d9b1d878ef78dffa4d
  device_info_plus: a56e6e74dbbd2bb92f2da12c64ddd4f67a749041
  flutter_web_auth_2: 62b08da29f15a20fa63f144234622a1488d45b65
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  package_info_plus: f0052d280d17aa382b932f399edf32507174e870
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  screen_retriever_macos: 452e51764a9e1cdb74b3c541238795849f21557f
  url_launcher_macos: 0fba8ddabfc33ce0a9afe7c5fef5aab3d8d2d673
  window_manager: 1d01fa7ac65a6e6f83b965471b1a7fdd3f06166c
  window_to_front: 9e76fd432e36700a197dac86a0011e49c89abe0a

PODFILE CHECKSUM: 9ebaf0ce3d369aaa26a9ea0e159195ed94724cf3

COCOAPODS: 1.16.2
