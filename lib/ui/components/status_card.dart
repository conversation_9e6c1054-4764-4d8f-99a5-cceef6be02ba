import 'package:flutter/material.dart';
import '../../data/models/dashboard_card.dart';
import '../../data/models/card_status.dart';

class StatusCard extends StatelessWidget {
  final DashboardCard card;
  final VoidCallback? onTap;

  const StatusCard({
    super.key,
    required this.card,
    this.onTap,
  });

  Color _getStatusColor() {
    switch (card.status) {
      case CardStatus.inProgress:
        return const Color(0xFF3B82F6); // Blue
      case CardStatus.done:
        return const Color(0xFF10B981); // Green
      case CardStatus.notGood:
        return const Color(0xFFEF4444); // Red
      case CardStatus.notStarted:
        return const Color(0xFF6B7280); // Gray
    }
  }

  Color _getCardBackgroundColor() {
    switch (card.status) {
      case CardStatus.inProgress:
        return const Color(0xFF1E3A8A).withValues(alpha: 0.1); // Blue tint
      case CardStatus.done:
        return const Color(0xFF065F46).withValues(alpha: 0.1); // Green tint
      case CardStatus.notGood:
        return const Color(0xFF991B1B).withValues(alpha: 0.1); // Red tint
      case CardStatus.notStarted:
        return const Color(0xFF374151).withValues(alpha: 0.1); // Gray tint
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: _getCardBackgroundColor(),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _getStatusColor().withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // Icon
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getStatusColor().withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    card.icon,
                    color: _getStatusColor(),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),

                // Title
                Expanded(
                  child: Text(
                    card.title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),

                // Status badge
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    card.status.displayName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
