import 'package:flutter/material.dart';
import '../../data/models/dashboard_card.dart';
import 'task_card.dart';

class CategorySection extends StatelessWidget {
  final String title;
  final String timeRange;
  final List<DashboardCard> tasks;
  final Color backgroundColor;
  final Function(int, String)? onStatusChange;

  const CategorySection({
    super.key,
    required this.title,
    required this.timeRange,
    required this.tasks,
    required this.backgroundColor,
    this.onStatusChange,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      margin: const EdgeInsets.all(8),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: Color(0xFF1A1A1A),
                ),
              ),
              Text(
                timeRange,
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF666666),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Tasks
          Expanded(
            child: ListView.builder(
              itemCount: tasks.length,
              itemBuilder: (context, index) {
                final task = tasks[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: TaskCard(
                    title: task.title,
                    status: task.status,
                    onStatusChange: (newStatus) {
                      onStatusChange?.call(index, newStatus);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
