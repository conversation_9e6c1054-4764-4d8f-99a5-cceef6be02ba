import 'package:flutter/material.dart';
import '../../data/models/card_status.dart';

class TaskCard extends StatelessWidget {
  final String title;
  final TaskStatus status;
  final Function(String)? onStatusChange;

  const TaskCard({
    super.key,
    required this.title,
    required this.status,
    this.onStatusChange,
  });

  Color _getStatusColor() {
    switch (status) {
      case TaskStatus.completed:
        return const Color(0xFF4CAF50); // Green
      case TaskStatus.onTrack:
        return const Color(0xFF2196F3); // Blue
      case TaskStatus.onHold:
        return const Color(0xFFFFC107); // Yellow
      case TaskStatus.started:
        return const Color(0xFFFF9800); // Orange
      case TaskStatus.notStarted:
        return const Color(0xFFF44336); // Red
    }
  }

  Color _getBackgroundColor() {
    switch (status) {
      case TaskStatus.completed:
        return const Color(0xFFE8F5E9); // Light Green
      default:
        return const Color(0xFFF5F5F5); // Light Gray
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and status icon
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    // Icon placeholder
                    Container(
                      width: 35,
                      height: 35,
                      decoration: BoxDecoration(
                        color: const Color(0xFFF5F5F5),
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Title
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
                // Status circle
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: _getStatusColor(),
                      width: 2,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Status bar
            GestureDetector(
              onTap: () {
                // Show status selection modal
                _showStatusModal(context);
              },
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _getBackgroundColor(),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    status.displayName,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: _getStatusColor(),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showStatusModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.5,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Select Status',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const Divider(),
            
            // Status options
            Expanded(
              child: ListView(
                children: TaskStatus.values.map((statusOption) {
                  final isSelected = statusOption == status;
                  return ListTile(
                    leading: Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _getColorForStatus(statusOption),
                      ),
                    ),
                    title: Text(
                      statusOption.displayName,
                      style: TextStyle(
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                    trailing: isSelected 
                        ? const Icon(Icons.check, color: Color(0xFF4CAF50))
                        : const Icon(Icons.circle_outlined, color: Color(0xFF666666)),
                    onTap: () {
                      onStatusChange?.call(statusOption.displayName);
                      Navigator.pop(context);
                    },
                    tileColor: isSelected ? const Color(0xFFE8F5E9) : null,
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getColorForStatus(TaskStatus taskStatus) {
    switch (taskStatus) {
      case TaskStatus.completed:
        return const Color(0xFF4CAF50);
      case TaskStatus.onTrack:
        return const Color(0xFF2196F3);
      case TaskStatus.onHold:
        return const Color(0xFFFFC107);
      case TaskStatus.started:
        return const Color(0xFFFF9800);
      case TaskStatus.notStarted:
        return const Color(0xFFF44336);
    }
  }
}
