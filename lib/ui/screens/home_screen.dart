import 'package:flutter/material.dart';
import '../components/app_header.dart';
import '../components/category_section.dart';
import '../../data/models/dashboard_data.dart';
import '../../data/models/category.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late List<Category> _categories;

  @override
  void initState() {
    super.initState();
    _categories = DashboardData.getCategories();
  }

  void _handleStatusChange(int categoryIndex, int taskIndex, String newStatus) {
    // Handle status change logic here
    setState(() {
      // Update the task status
      // This would typically involve updating the data source
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          const AppHeader(),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.all(8),
              child: Row(
                children: _categories.map((category) {
                  return Container(
                    width: 300,
                    margin: const EdgeInsets.only(right: 8),
                    child: CategorySection(
                      title: category.title,
                      timeRange: _getTimeRange(category.id),
                      tasks: category.cards,
                      backgroundColor: category.color,
                      onStatusChange: (taskIndex, newStatus) {
                        final categoryIndex = _categories.indexOf(category);
                        _handleStatusChange(categoryIndex, taskIndex, newStatus);
                      },
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getTimeRange(String categoryId) {
    switch (categoryId) {
      case 'personal':
        return '6AM - 8AM';
      case 'learn':
        return '8AM - 12PM';
      case 'work':
        return '12PM - 6PM';
      case 'finance':
        return '6PM - 8PM';
      case 'weekend':
        return 'SAT - SUN';
      default:
        return '6AM - 8AM';
    }
  }
}
