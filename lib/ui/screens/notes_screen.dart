import 'package:flutter/material.dart';
import '../components/app_header.dart';

class NotesScreen extends StatefulWidget {
  const NotesScreen({super.key});

  @override
  State<NotesScreen> createState() => _NotesScreenState();
}

class _NotesScreenState extends State<NotesScreen> {
  String _activeTab = 'FAMILY';
  final List<String> _tabs = ['FAMILY', 'LEARN', 'WORK'];

  final List<Map<String, String>> _familyMembers = [
    {'name': '<PERSON>', 'relation': 'SELF'},
    {'name': '<PERSON><PERSON>', 'relation': 'BROTHER'},
    {'name': '<PERSON><PERSON>', 'relation': 'BROTHER'},
    {'name': '<PERSON><PERSON><PERSON>', 'relation': 'BROTHER'},
    {'name': '<PERSON><PERSON>', 'relation': 'FATHER'},
    {'name': '<PERSON><PERSON><PERSON>', 'relation': 'MOTHER'},
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          const AppHeader(),
          
          // Tab Bar
          Container(
            color: Colors.white,
            child: Row(
              children: _tabs.map((tab) {
                final isActive = tab == _activeTab;
                return Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _activeTab = tab;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: isActive ? Colors.black : Colors.transparent,
                            width: 2,
                          ),
                        ),
                      ),
                      child: Center(
                        child: Text(
                          tab,
                          style: TextStyle(
                            fontSize: 14,
                            color: isActive ? Colors.black : const Color(0xFF666666),
                            fontWeight: isActive ? FontWeight.w500 : FontWeight.normal,
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
          
          // Content
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    switch (_activeTab) {
      case 'FAMILY':
        return Padding(
          padding: const EdgeInsets.all(16),
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.8,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: _familyMembers.length,
            itemBuilder: (context, index) {
              final member = _familyMembers[index];
              return Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: const Color(0xFFE0E0E0), width: 2),
                ),
                padding: const EdgeInsets.all(10),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Profile image placeholder
                    Expanded(
                      flex: 3,
                      child: Container(
                        width: double.infinity,
                        margin: const EdgeInsets.only(bottom: 18, top: 15),
                        decoration: const BoxDecoration(
                          color: Color(0xFFF0F0F0),
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                    
                    // Name
                    Text(
                      member['name']!,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 2),
                    
                    // Relation
                    Text(
                      member['relation']!,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF666666),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      case 'LEARN':
        return const Center(
          child: Text('Learn content coming soon'),
        );
      case 'WORK':
        return const Center(
          child: Text('Work content coming soon'),
        );
      default:
        return const SizedBox.shrink();
    }
  }
}
