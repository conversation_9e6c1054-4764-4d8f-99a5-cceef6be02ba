import 'package:flutter/material.dart';
import '../components/app_header.dart';

class WalletScreen extends StatelessWidget {
  const WalletScreen({super.key});

  final Map<String, List<String>> _categories = const {
    'HOME': ['Tasks', 'Attendance'],
    'OPERATIONS': ['Stocks', 'Crypto'],
    'FINANCE': ['Budget', 'Transactions'],
    'PEOPLE': ['Leave', 'Job'],
    'MSSS': ['Bugs', 'Tickets'],
  };

  final Map<String, Color> _categoryColors = const {
    'HOME': Color(0xFF4CAF50),
    'OPERATIONS': Color(0xFF2196F3),
    'FINANCE': Color(0xFFFF9800),
    'PEOPLE': Color(0xFF9C27B0),
    'MSSS': Color(0xFFF44336),
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          const AppHeader(),
          
          // Profile Card
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Logo
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Center(
                    child: Text(
                      'SXE',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                
                // Info
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Sharif',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Administration',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF666666),
                        ),
                      ),
                      Text(
                        'CFO',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF666666),
                        ),
                      ),
                    ],
                  ),
                ),
                
                const Icon(Icons.swap_vert, size: 24),
              ],
            ),
          ),
          
          // Categories
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              children: _categories.entries.map((entry) {
                return _buildCategoryCard(entry.key, entry.value);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryCard(String title, List<String> items) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _categoryColors[title],
              ),
            ),
          ),
          
          // Items
          ...items.map((item) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE0E0E0), width: 1),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _getItemIcon(title, item),
                    size: 24,
                    color: Colors.black,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          _getItemStatus(title, item),
                          style: const TextStyle(
                            fontSize: 14,
                            color: Color(0xFF666666),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  IconData _getItemIcon(String category, String item) {
    switch (item) {
      case 'Tasks':
        return Icons.task_alt;
      case 'Attendance':
        return Icons.access_time;
      case 'Stocks':
        return Icons.trending_up;
      case 'Crypto':
        return Icons.currency_bitcoin;
      case 'Budget':
        return Icons.account_balance_wallet;
      case 'Transactions':
        return Icons.receipt;
      case 'Leave':
        return Icons.event_busy;
      case 'Job':
        return Icons.work;
      case 'Bugs':
        return Icons.bug_report;
      case 'Tickets':
        return Icons.confirmation_number;
      default:
        return Icons.circle;
    }
  }

  String _getItemStatus(String category, String item) {
    final statusMap = {
      'Tasks': 'Pending',
      'Attendance': 'Present',
      'Stocks': 'Trading',
      'Crypto': 'Volatile',
      'Budget': 'Review',
      'Transactions': 'Pending',
      'Leave': 'Open',
      'Job': 'Hiring',
      'Bugs': 'Critical',
      'Tickets': 'Open',
    };
    return statusMap[item] ?? 'Active';
  }
}
