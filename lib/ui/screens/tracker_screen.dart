import 'package:flutter/material.dart';
import '../components/app_header.dart';

class TrackerScreen extends StatefulWidget {
  const TrackerScreen({super.key});

  @override
  State<TrackerScreen> createState() => _TrackerScreenState();
}

class _TrackerScreenState extends State<TrackerScreen> {
  final Map<String, bool> _checklistItems = {
    'wakeup': false,
    'exercise': false,
    'diet': false,
    'learn': false,
    'work': false,
    'finances': false,
    'steps': false,
    'sleep': false,
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          const AppHeader(),
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(12),
              children: [
                // Checklist items
                _buildTrackingItem(
                  icon: Icons.wb_sunny_outlined,
                  label: 'WAKEUP',
                  isChecked: _checklistItems['wakeup']!,
                  onTap: () => _toggleItem('wakeup'),
                ),
                _buildTrackingItem(
                  icon: Icons.fitness_center,
                  label: 'EXERCISE',
                  isChecked: _checklistItems['exercise']!,
                  onTap: () => _toggleItem('exercise'),
                ),
                _buildTrackingItem(
                  icon: Icons.restaurant,
                  label: 'DIET',
                  isChecked: _checklistItems['diet']!,
                  onTap: () => _toggleItem('diet'),
                ),
                _buildTrackingItem(
                  icon: Icons.school,
                  label: 'LEARN',
                  isChecked: _checklistItems['learn']!,
                  onTap: () => _toggleItem('learn'),
                ),
                _buildTrackingItem(
                  icon: Icons.work,
                  label: 'WORK',
                  isChecked: _checklistItems['work']!,
                  onTap: () => _toggleItem('work'),
                ),
                _buildTrackingItem(
                  icon: Icons.account_balance,
                  label: 'FINANCES',
                  isChecked: _checklistItems['finances']!,
                  onTap: () => _toggleItem('finances'),
                ),
                _buildTrackingItem(
                  icon: Icons.directions_walk,
                  label: 'STEPS',
                  isChecked: _checklistItems['steps']!,
                  onTap: () => _toggleItem('steps'),
                ),
                _buildTrackingItem(
                  icon: Icons.bedtime,
                  label: 'SLEEP',
                  isChecked: _checklistItems['sleep']!,
                  onTap: () => _toggleItem('sleep'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrackingItem({
    required IconData icon,
    required String label,
    required bool isChecked,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Icon(icon, size: 24, color: Colors.black),
            const SizedBox(width: 26),
            Expanded(
              child: Text(
                label,
                style: const TextStyle(
                  fontSize: 17,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            if (isChecked) ...[
              const Text(
                'DONE',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF4CAF50),
                ),
              ),
              const SizedBox(width: 6),
              const Icon(
                Icons.check_circle,
                size: 24,
                color: Color(0xFF4CAF50),
              ),
            ] else
              const Icon(
                Icons.circle_outlined,
                size: 24,
                color: Colors.grey,
              ),
          ],
        ),
      ),
    );
  }

  void _toggleItem(String key) {
    setState(() {
      _checklistItems[key] = !_checklistItems[key]!;
    });
  }
}
