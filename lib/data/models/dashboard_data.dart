import 'package:flutter/material.dart';
import 'category.dart';
import 'dashboard_card.dart';
import 'card_status.dart';

class DashboardData {
  static List<Category> getCategories() {
    return [
      Category(
        id: 'personal',
        title: 'MIND & BODY',
        color: const Color(0xFFE3F2FD), // Light Blue
        cards: [
          const DashboardCard(
            id: 'prayers',
            title: 'PRAYERS',
            icon: Icons.mosque,
            status: TaskStatus.onTrack,
            categoryId: 'personal',
          ),
          const DashboardCard(
            id: 'exercise',
            title: 'EXERCISE',
            icon: Icons.fitness_center,
            status: TaskStatus.onTrack,
            categoryId: 'personal',
          ),
          const DashboardCard(
            id: 'diet',
            title: 'DIET',
            icon: Icons.restaurant,
            status: TaskStatus.onTrack,
            categoryId: 'personal',
          ),
          const DashboardCard(
            id: 'family',
            title: 'FAMILY',
            icon: Icons.family_restroom,
            status: TaskStatus.notStarted,
            categoryId: 'personal',
          ),
          const DashboardCard(
            id: 'social',
            title: 'SOCIAL',
            icon: Icons.people,
            status: TaskStatus.completed,
            categoryId: 'personal',
          ),
        ],
      ),
      Category(
        id: 'learn',
        title: 'LEARN',
        color: const Color(0xFFE8F5E9), // Light Green
        cards: [
          const DashboardCard(
            id: 'coding',
            title: 'CODING',
            icon: Icons.code,
            status: TaskStatus.completed,
            categoryId: 'learn',
          ),
          const DashboardCard(
            id: 'design',
            title: 'DESIGN',
            icon: Icons.design_services,
            status: TaskStatus.completed,
            categoryId: 'learn',
          ),
          const DashboardCard(
            id: 'business',
            title: 'BUSINESS',
            icon: Icons.business,
            status: TaskStatus.completed,
            categoryId: 'learn',
          ),
          const DashboardCard(
            id: 'marketing',
            title: 'MARKETING',
            icon: Icons.campaign,
            status: TaskStatus.completed,
            categoryId: 'learn',
          ),
          const DashboardCard(
            id: 'finance',
            title: 'FINANCE',
            icon: Icons.account_balance,
            status: TaskStatus.completed,
            categoryId: 'learn',
          ),
        ],
      ),
      Category(
        id: 'work',
        title: 'WORK',
        color: const Color(0xFFFFF3E0), // Light Orange
        cards: [
          const DashboardCard(
            id: 'trading',
            title: 'TRADING',
            icon: Icons.trending_up,
            status: TaskStatus.onTrack,
            categoryId: 'work',
          ),
          const DashboardCard(
            id: 'real_estate',
            title: 'REAL ESTATE',
            icon: Icons.home,
            status: TaskStatus.onTrack,
            categoryId: 'work',
          ),
          const DashboardCard(
            id: 'digital_marketing',
            title: 'DIGITAL MARKETING',
            icon: Icons.campaign,
            status: TaskStatus.onTrack,
            categoryId: 'work',
          ),
          const DashboardCard(
            id: 'hubcv',
            title: 'HUBCV',
            icon: Icons.work,
            status: TaskStatus.notStarted,
            categoryId: 'work',
          ),
          const DashboardCard(
            id: 'codelude',
            title: 'CODELUDE',
            icon: Icons.code,
            status: TaskStatus.notStarted,
            categoryId: 'work',
          ),
        ],
      ),
      Category(
        id: 'finance',
        title: 'FINANCE',
        color: const Color(0xFFF3E5F5), // Light Purple
        cards: [
          const DashboardCard(
            id: 'net_worth',
            title: 'NET WORTH',
            icon: Icons.account_balance,
            status: TaskStatus.onTrack,
            categoryId: 'finance',
          ),
          const DashboardCard(
            id: 'investments',
            title: 'INVESTMENTS',
            icon: Icons.trending_up,
            status: TaskStatus.onTrack,
            categoryId: 'finance',
          ),
          const DashboardCard(
            id: 'credits',
            title: 'CREDITS',
            icon: Icons.credit_card,
            status: TaskStatus.onTrack,
            categoryId: 'finance',
          ),
          const DashboardCard(
            id: 'charity',
            title: 'CHARITY',
            icon: Icons.volunteer_activism,
            status: TaskStatus.onTrack,
            categoryId: 'finance',
          ),
          const DashboardCard(
            id: 'assets',
            title: 'ASSETS',
            icon: Icons.home,
            status: TaskStatus.onTrack,
            categoryId: 'finance',
          ),
        ],
      ),
      Category(
        id: 'weekend',
        title: 'WEEKEND',
        color: const Color(0xFFFFEBEE), // Light Pink
        cards: [
          const DashboardCard(
            id: 'events',
            title: 'EVENTS',
            icon: Icons.event,
            status: TaskStatus.onTrack,
            categoryId: 'weekend',
          ),
          const DashboardCard(
            id: 'memories',
            title: 'MEMORIES',
            icon: Icons.photo_album,
            status: TaskStatus.onTrack,
            categoryId: 'weekend',
          ),
          const DashboardCard(
            id: 'shopping',
            title: 'SHOPPING',
            icon: Icons.shopping_bag,
            status: TaskStatus.onTrack,
            categoryId: 'weekend',
          ),
          const DashboardCard(
            id: 'travels',
            title: 'TRAVELS',
            icon: Icons.flight,
            status: TaskStatus.onTrack,
            categoryId: 'weekend',
          ),
          const DashboardCard(
            id: 'documents',
            title: 'DOCUMENTS',
            icon: Icons.description,
            status: TaskStatus.onTrack,
            categoryId: 'weekend',
          ),
        ],
      ),
    ];
  }
}
