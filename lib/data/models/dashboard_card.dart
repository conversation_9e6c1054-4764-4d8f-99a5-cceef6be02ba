import 'package:flutter/material.dart';
import 'card_status.dart';

class DashboardCard {
  final String id;
  final String title;
  final IconData icon;
  final TaskStatus status;
  final String categoryId;

  const DashboardCard({
    required this.id,
    required this.title,
    required this.icon,
    required this.status,
    required this.categoryId,
  });

  DashboardCard copyWith({
    String? id,
    String? title,
    IconData? icon,
    TaskStatus? status,
    String? categoryId,
  }) {
    return DashboardCard(
      id: id ?? this.id,
      title: title ?? this.title,
      icon: icon ?? this.icon,
      status: status ?? this.status,
      categoryId: categoryId ?? this.categoryId,
    );
  }
}
