import 'package:flutter/material.dart';
import 'dashboard_card.dart';

class Category {
  final String id;
  final String title;
  final Color color;
  final List<DashboardCard> cards;

  const Category({
    required this.id,
    required this.title,
    required this.color,
    required this.cards,
  });

  Category copyWith({
    String? id,
    String? title,
    Color? color,
    List<DashboardCard>? cards,
  }) {
    return Category(
      id: id ?? this.id,
      title: title ?? this.title,
      color: color ?? this.color,
      cards: cards ?? this.cards,
    );
  }
}
