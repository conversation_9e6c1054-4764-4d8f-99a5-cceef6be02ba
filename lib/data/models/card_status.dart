enum TaskStatus {
  notStarted,
  started,
  onTrack,
  onHold,
  completed,
}

extension TaskStatusExtension on TaskStatus {
  String get displayName {
    switch (this) {
      case TaskStatus.notStarted:
        return 'NOT STARTED';
      case TaskStatus.started:
        return 'STARTED';
      case TaskStatus.onTrack:
        return 'ON TRACK';
      case TaskStatus.onHold:
        return 'ON HOLD';
      case TaskStatus.completed:
        return 'COMPLETED';
    }
  }

  String get colorHex {
    switch (this) {
      case TaskStatus.notStarted:
        return '#F44336'; // Red
      case TaskStatus.started:
        return '#FF9800'; // Orange
      case TaskStatus.onTrack:
        return '#2196F3'; // Blue
      case TaskStatus.onHold:
        return '#FFC107'; // Yellow
      case TaskStatus.completed:
        return '#4CAF50'; // Green
    }
  }
}
