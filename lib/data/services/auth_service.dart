import 'package:appwrite/appwrite.dart';
import 'package:appwrite/models.dart';

class AuthService {
  static const String appwriteProjectId = '6892321a001a1e4a17d4';
  static const String appwriteEndpoint = 'http://31.97.229.201/v1';

  late final Client _client;
  late final Account _account;

  AuthService._internal() {
    _client =
        Client().setProject(appwriteProjectId).setEndpoint(appwriteEndpoint);
    _account = Account(_client);
  }

  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;

  /// Get current user session
  Future<User?> getCurrentUser() async {
    try {
      final user = await _account.get();
      return user;
    } catch (e) {
      return null;
    }
  }

  /// Sign up a new user
  Future<User> signUp({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      // Create the account
      await _account.create(
        userId: ID.unique(),
        email: email,
        password: password,
        name: name,
      );

      // Automatically sign in the user
      await _account.createEmailPasswordSession(
        email: email,
        password: password,
      );

      // Get and return user data
      final user = await _account.get();
      return user;
    } catch (e) {
      throw Exception('Sign up failed: ${e.toString()}');
    }
  }

  /// Sign in an existing user
  Future<User> signIn({
    required String email,
    required String password,
  }) async {
    try {
      await _account.createEmailPasswordSession(
        email: email,
        password: password,
      );
      final user = await _account.get();
      return user;
    } catch (e) {
      throw Exception('Sign in failed: ${e.toString()}');
    }
  }

  /// Sign out the current user
  Future<void> signOut() async {
    try {
      await _account.deleteSession(sessionId: 'current');
    } catch (e) {
      throw Exception('Sign out failed: ${e.toString()}');
    }
  }

  /// Check if user is logged in
  Future<bool> isLoggedIn() async {
    try {
      await _account.get();
      return true;
    } catch (e) {
      return false;
    }
  }
}
